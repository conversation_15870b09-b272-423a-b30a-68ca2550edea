<template>
  <div class="home-map-container">
    <div id="map" class="home-map-div" />
    <!-- 地图加载动画 -->
    <div v-if="isMapLoading" class="map-loading-overlay">
      <NSpin size="large" :rotate="true">
        <template #icon>
          <div class="loading-icon">
            <svg viewBox="0 0 1024 1024" width="32" height="32">
              <path
                d="M546.462897 266.292966c-73.410207 0-133.15531-59.727448-133.155311-133.137656C413.307586 59.762759 473.05269 0 546.462897 0c73.410207 0 133.12 59.727448 133.12 133.15531 0 73.410207-59.709793 133.137655-133.12 133.137656z m-283.453794 105.736827c-67.054345 0-121.626483-54.554483-121.626482-121.644138s54.572138-121.644138 121.626482-121.644138a121.767724 121.767724 0 0 1 121.608828 121.644138c0 67.054345-54.554483 121.644138-121.608828 121.644138zM142.547862 647.185655A107.343448 107.343448 0 0 1 35.310345 539.895172a107.343448 107.343448 0 0 1 107.237517-107.237517 107.343448 107.343448 0 0 1 107.219862 107.237517 107.343448 107.343448 0 0 1-107.219862 107.272828z m120.461241 272.595862a91.047724 91.047724 0 0 1-90.941793-90.959448 91.065379 91.065379 0 0 1 90.924138-90.941793 91.065379 91.065379 0 0 1 90.941793 90.941793c0 50.14069-40.783448 90.959448-90.924138 90.959448zM546.462897 1024a79.518897 79.518897 0 0 1-79.448276-79.448276c0-43.820138 35.645793-79.448276 79.448276-79.448276a79.518897 79.518897 0 0 1 79.43062 79.448276c0 43.820138-35.628138 79.448276-79.448276 79.448276z m287.744-134.285241a64.194207 64.194207 0 0 1-64.123587-64.123587 64.194207 64.194207 0 0 1 64.123587-64.123586 64.194207 64.194207 0 0 1 64.123586 64.123586 64.194207 64.194207 0 0 1-64.123586 64.123587z m117.848275-296.695173a52.683034 52.683034 0 0 1-52.612413-52.612414 52.683034 52.683034 0 0 1 52.612413-52.630069 52.70069 52.70069 0 0 1 52.630069 52.612414 52.718345 52.718345 0 0 1-52.630069 52.630069z m-158.667034-338.696827a40.818759 40.818759 0 1 0 81.655172 0.017655 40.818759 40.818759 0 0 0-81.655172 0z"
                fill="#18a058" />
            </svg>
          </div>
        </template>
        <template #description>
          <span class="loading-text">地图加载中...</span>
        </template>
      </NSpin>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref } from 'vue'
import { NSpin } from 'naive-ui'
import { homeMap } from './homeMap'

const getCurrentFloor = ref('F1')
const terminalBuilding = ref([
  {
    url: "https://iserver.supermap.io/iserver/services/map-china400/rest/maps/China",
    name: "一层专题-rest",
    floor: "F1",
  },
  {
    url: "https://iserver.supermap.io/iserver/services/map-china400/rest/maps/China",
    name: "一层夹层专题-rest",
    floor: "F1J",
  },
  {
    url: "https://iserver.supermap.io/iserver/services/map-china400/rest/maps/China",
    name: "二层专题-rest",
    floor: "F2",
  },
  {
    url: "https://iserver.supermap.io/iserver/services/map-china400/rest/maps/China",
    name: "三层专题-rest",
    floor: "F3",
  },
  {
    url: "https://iserver.supermap.io/iserver/services/map-china400/rest/maps/China",
    name: "三层夹层专题-rest",
    floor: "F3J",
  },
  {
    url: "https://iserver.supermap.io/iserver/services/map-china400/rest/maps/China",
    name: "地下一层专题-rest",
    floor: "B1",
  },
  {
    url: "https://iserver.supermap.io/iserver/services/map-china400/rest/maps/China",
    name: "地下二层专题-rest",
    floor: "B2",
  },
]);

const isMapLoading = ref(true)
// 组件卸载时清理
onBeforeUnmount(() => {
  homeMap.destroy()
})

onMounted(async () => {
  try {
    // 初始化地图
    await homeMap.initHomeMap(terminalBuilding.value, getCurrentFloor.value)

    // 监听地图加载状态变化
    const checkLoadingState = () => {
      isMapLoading.value = homeMap.getMapLoadingState()
      if (isMapLoading.value) {
        setTimeout(checkLoadingState, 100)
      }
    }
    checkLoadingState()

  } catch (error) {
    console.error('地图初始化失败:', error)
  }
})
</script>

<style scoped>
.home-map-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.home-map-div {
  margin: 0 auto;
  width: 100%;
  height: 100%;
}

/* 地图加载动画样式 */
.map-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}

.loading-icon {
  display: flex;
  align-items: center;
  justify-content: center;

  svg {
    animation: spin 1s linear infinite;
  }
}

.loading-text {
  color: #18a058;
  font-size: 14px;
  font-weight: 500;
  margin-top: 8px;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* 基础地图样式 */
:deep(.leaflet-container) {
  width: 100%;
  height: 100%;
  cursor: grab;
}

:deep(.leaflet-container:active) {
  cursor: grabbing;
}

/* 确保地图可以接收鼠标事件 */
:deep(.leaflet-pane) {
  pointer-events: auto !important;
}

:deep(.leaflet-tile-pane),
:deep(.leaflet-overlay-pane),
:deep(.leaflet-marker-pane),
:deep(.leaflet-tooltip-pane),
:deep(.leaflet-popup-pane) {
  pointer-events: auto !important;
}

:deep(.custom-popup-container) {
  width: 600px;

  .n-card>.n-card-header {
    padding: 10px;
  }

  .leaflet-popup-content-wrapper {
    border-radius: 4px;
    padding: 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    background: #fff;
  }

  .leaflet-popup-content {
    margin: 0;
    width: 100% !important;
  }

  .leaflet-popup-tip-container {
    .leaflet-popup-tip {
      background: #fff;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
  }

  .popup-footer {
    display: flex;
    justify-content: center;
    margin-top: 10px;
  }
}

:deep(.leaflet-container a.leaflet-popup-close-button) {
  right: 10px !important;
  top: 10px !important;
  background: red;
}

:deep(.leaflet-control-scale) {
  margin-right: 20px;
  margin-bottom: 20px;

  .leaflet-control-scale-line {
    border-color: #666;
    color: #333;
    background-color: rgba(255, 255, 255, 0.8);
    padding: 2px 5px;
    font-size: 12px;
  }
}

/* 环境数据标签样式 */
:deep(.environment-data-label) {
  border: none !important;
  background: transparent !important;

  .leaflet-marker-icon {
    border: none !important;
    background: transparent !important;
  }
}
</style>
