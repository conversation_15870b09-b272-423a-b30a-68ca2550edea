import {
  MAP_UTIL,
  type MapConfig,
  type LayerData,
  type MapEventCallbacks,
  type DrawPointOptions,
  type HeatmapData,
  type HeatmapOptions,
} from "../utils/mapUtil";
import L from 'leaflet'

// 声明全局 L 对象
declare const L: any;

// 环境区域数据接口
export interface EnvironmentRegion {
  id: string;
  coordinates: [number, number][];
  temperature: number;
  humidity: number;
  lighting: number;
  properties?: Record<string, any>;
}

// 图例切换字段数据接口
export interface LegendTogglePayload {
  key: string | unknown;
  index: number | unknown;
  switchValue: boolean | unknown;
}

export class HomeMap {
  private currentFloor: string = "F1";
  private vectorLayers: Record<string, any> = {};
  private environmentPolygons: any[] = [];
  private environmentPolygonIds: string[] = [];
  private environmentRegionsData: EnvironmentRegion[] = [];
  private isHomeMapReady: boolean = false;
  private isMapLoading: boolean = true;
  private isDrawing: boolean = false; // 添加绘制状态标识
  private currentDrawingType: "line" | "polygon" | null = null; // 当前绘制类型

  private clickCallbacks: {
    onPointClick?: (pointData: any, entity: any) => void;
    onPolygonClick?: (polygonData: any, entity: any) => void;
    onLineClick?: (lineData: any, entity: any) => void;
  } = {};

  // 添加一个回调函数来同步楼层状态
  private onFloorChange?: (floor: string) => void;

  // 测量结果存储
  private measureResults: Array<{
    type: "distance" | "area";
    layer: any;
    timestamp?: number;
  }> = [];

  // 绘制事件处理器
  private drawingEventHandlers: {
    map: any;
    handlers: {
      click: (e: any) => void;
      mousemove: (e: any) => void;
      contextmenu: (e: any) => void;
    };
    markers?: any[];
    tempLine?: any;
  } | null = null;

  // 设置楼层变化回调
  setFloorChangeCallback(callback: (floor: string) => void): void {
    this.onFloorChange = callback;
  }

  /**
   * 初始化地图
   */
  async initHomeMap(
    terminalBuilding: any[],
    defaultFloor: string,
  ): Promise<any> {
    try {
      this.isMapLoading = true;

      // 地图配置
      const config: MapConfig = {
        center: [35.25, 102.55],
        zoom: 4,
        maxZoom: 18,
        minZoom: 1,
        rotate: false, // 禁用旋转
        attributionControl: false,
        zoomControl: false,
        dragging: true,
        touchZoom: true,
        scrollWheelZoom: true,
        doubleClickZoom: true,
        boxZoom: true,
        keyboard: true,
        // bearing: 35, // 移除旋转角度
        backgroundColor: '#f0f0f0',
      };

      // 图层数据
      const layers: LayerData[] = terminalBuilding.map((item) => ({
        floor: item.floor,
        url: item.url,
      }));

      // 事件回调
      const callbacks: MapEventCallbacks = {
        onMapClick: (event) => {
          console.log("地图点击事件", event);
        },
        onPointClick: (point, _event) => {
          console.log("点标记点击", point);
        },
        onZoomEnd: (zoom) => {
          console.log("缩放结束", zoom);
        },
        onMoveEnd: (center) => {
          console.log("移动结束", center);
        },
      };

      // 设置地图加载完成回调
      MAP_UTIL.setMapReadyCallback((result) => {
        this.isHomeMapReady = true;
        this.isMapLoading = false;
        console.log("HomeMap 初始化成功", result);
      });

      // 初始化地图
      await MAP_UTIL.initMap("map", config, layers, callbacks);

      // 设置默认图层
      MAP_UTIL.switchLayer(defaultFloor);
      this.currentFloor = defaultFloor;

      return MAP_UTIL.getMap();
    } catch (error) {
      console.error("HomeMap 初始化失败:", error);
      this.isMapLoading = false;
      throw error;
    }
  }

  /**
   * 切换楼层
   */
  switchFloor(floorName: string): void {
    if (!MAP_UTIL.isReady()) return;
    MAP_UTIL.switchLayer(floorName);
    this.currentFloor = floorName;

    // 通知外部楼层状态变化
    if (this.onFloorChange) {
      this.onFloorChange(floorName);
    }

    // 楼层切换后，只需要控制当前楼层的图层显隐，不需要重新绘制
    this.updateLayerVisibilityForCurrentFloor();
  }

  /**
   * 根据当前楼层更新图层显隐状态
   */
  private updateLayerVisibilityForCurrentFloor(): void {
    // 遍历所有楼层的矢量图层
    Object.keys(this.vectorLayers).forEach((floorName) => {
      if (floorName === this.currentFloor) {
        this.vectorLayers[floorName].forEach((item: any) => {
          this.showLayerItem(item);
        });
      } else {
        this.vectorLayers[floorName].forEach((item: any) => {
          this.hideLayerItem(item);
        });
      }
    });
  }

  /**
   * 显示图层项
   */
  private showLayerItem(item: any): void {
    if (!item || !item.entity) return;

    try {
      // 通过将实体添加到地图来控制显示
      if (item.entity.addTo && MAP_UTIL.getMap()) {
        item.entity.addTo(MAP_UTIL.getMap());
      }
    } catch (error) {
      console.warn(`显示图层项失败: ${item.id}`, error);
    }
  }

  /**
   * 隐藏图层项
   */
  private hideLayerItem(item: any): void {
    if (!item || !item.entity) return;

    try {
      // 通过从地图中移除实体来控制隐藏
      if (item.entity.remove) {
        item.entity.remove();
      }
    } catch (error) {
      console.warn(`隐藏图层项失败: ${item.id}`, error);
    }
  }

  /**
   * 根据图层layerKey隐藏图层
   */
  hideLayerByKey(key: string) {
    if (!MAP_UTIL.isReady()) return;

    this.vectorLayers[this.currentFloor].forEach((item: any) => {
      if (item.layerKey === key) {
        this.hideLayerItem(item);
      }
    });

    // Filter out the items with matching layerKey
    this.vectorLayers[this.currentFloor] = this.vectorLayers[
      this.currentFloor
    ].filter((item: any) => item.layerKey !== key);
  }

  /**
   * 切换矢量图层显示
   */
  toggleVectorLayer(payload: LegendTogglePayload | any): void {
    if (!MAP_UTIL.isReady()) return;

    const { key, switchValue, points } = payload;

    if (!switchValue) {
      let count = 0;
      this.vectorLayers[this.currentFloor].forEach((item: any) => {
        if (item.layerKey === key) {
          this.hideLayerItem(item);
          count++;
        }
      });
      if (count === 0) {
        MAP_UTIL.showMessage("该数据不在当前图层");
      }
    } else {
      // 绘制点位
      points.forEach((point: any) => {
        const data = point;
        if (!point.position) {
          point = point.mapInfo;
        }
        // 确保每个点的ID是唯一的，使用图层key和原始ID组合
        const pointId = `${key}_${point.id}`;
        // 确定存储楼层：如果指定了楼层则使用指定楼层，否则使用当前楼层
        const targetFloor = point.floor || this.currentFloor;

        // 检查点位是否已经存在（在目标楼层中检查）
        if (this.vectorLayers[targetFloor]) {
          const existingPoint = this.vectorLayers[targetFloor].find(
            (item: any) => item.id === pointId,
          );
          if (existingPoint) {
            console.log(`点位 ${pointId} 已存在，跳过绘制`);
            return;
          }
        }

        const pointOptions: DrawPointOptions = {
          id: pointId,
          label: point.name,
          layerKey: key,
          description: point.description || "", // 描述信息
          showLabel: false, // 不显示标签
          iconUrl: point.iconUrl || null, // 可以根据需要设置图标
          iconSize: point.iconSize || null,
          scale: point.scale || 1, // 添加缩放参数，默认为1
          data: data,
          floor: point.floor, // 添加楼层信息
          onClick: (entity) => {
            // 调用点位点击回调
            if (this.clickCallbacks.onPointClick) {
              this.clickCallbacks.onPointClick(data, entity);
            }
          },
        };

        const position = {
          longitude: point.position.lng,
          latitude: point.position.lat,
        };

        // 绘制点位并保存实体引用
        const entity = MAP_UTIL.drawPoint(position, pointOptions);

        // 将点位信息存储到对应的图层中
        if (!this.vectorLayers[point.floor]) {
          this.vectorLayers[point.floor] = [];
        }

        this.vectorLayers[point.floor].push(pointOptions);
        // 保存实体引用以便后续控制显隐
        if (entity) {
          const pointIndex = this.vectorLayers[point.floor].findIndex(
            (item: any) => item.id === pointId,
          );
          if (pointIndex !== -1) {
            this.vectorLayers[point.floor][pointIndex].entity = entity;
          }
        }
      });

      this.updateLayerVisibilityForCurrentFloor();
    }
  }

  /**
   * 处理环境区域数据更新
   */
  handleEnvironmentRegionsData(data: EnvironmentRegion[]): void {
    this.environmentRegionsData = data;
    if (Array.isArray(data) && data.length > 0) {
      setTimeout(() => {
        this.drawEnvironmentPolygons();
      }, 100);
    } else {
      this.removeEnvironmentPolygons();
    }
  }

  /**
   * 处理视图切换事件
   */
  handleViewSwitch(viewName: string): void {
    if (viewName === "环境视图") {
      setTimeout(() => {
        this.drawEnvironmentPolygons();
      }, 100);
    } else {
      this.removeEnvironmentPolygons();
    }
  }

  /**
   * 处理区域聚焦事件
   */
  handleRegionFocus(region: EnvironmentRegion): void {
    if (!MAP_UTIL.isReady() || !region.coordinates) return;

    const bounds = L.latLngBounds(region.coordinates);
    const center = bounds.getCenter();

    MAP_UTIL.setCenter([center.lat, center.lng]);
  }

  /**
   * 绘制点
   */
  drawPoint(
    position: { longitude: number; latitude: number },
    options: any = {},
  ): void {
    if (!MAP_UTIL.isReady()) return;

    const pointId = `${options.layerKey || options.name}_${options.id}`;

    // 确定图层key，如果没有指定，使用默认的'points'图层
    const layerKey = options.layerKey || "points";

    // 确定存储楼层：如果指定了楼层则使用指定楼层，否则使用当前楼层
    const targetFloor = options.floor || this.currentFloor;

    // 检查点位是否已经存在（在目标楼层中检查）
    if (this.vectorLayers[targetFloor]) {
      const existingPoint = this.vectorLayers[targetFloor].find(
        (item: any) => item.id === pointId,
      );
      if (existingPoint) {
        console.log(`点位 ${pointId} 已存在，跳过绘制`);
        return;
      }
    }

    // 将点位信息存储到目标楼层对应的图层中
    if (!this.vectorLayers[targetFloor]) {
      this.vectorLayers[targetFloor] = [];
    }

    const pointData = {
      id: pointId,
      position,
      options,
      floor: targetFloor,
      layerKey: layerKey, // 保存图层key，用于后续识别属于哪个图层
      type: "point", // 添加类型标识
    };

    this.vectorLayers[targetFloor].push(pointData);

    // 绘制点位并保存实体引用
    const entity = MAP_UTIL.drawPoint(position, {
      id: pointId,
      label: options.label || "",
      description: options.description || "",
      showLabel: options.showLabel || false,
      iconUrl: options.iconUrl,
      iconSize: options.iconSize || [32, 32],
      scale: options.scale || 1, // 添加缩放参数，默认为1
      data: options.data,
      onClick: (entity) => {
        // 调用点位点击回调
        if (this.clickCallbacks.onPointClick) {
          this.clickCallbacks.onPointClick(options.data, entity);
        }

        // 如果options中有onClick回调，直接调用它
        if (options.onClick && typeof options.onClick === "function") {
          options.onClick(entity);
        }
      },
    });

    // 保存实体引用以便后续控制显隐
    if (entity) {
      const pointIndex = this.vectorLayers[targetFloor].findIndex(
        (item: any) => item.id === pointId,
      );
      if (pointIndex !== -1) {
        this.vectorLayers[targetFloor][pointIndex].entity = entity;
      }
    }

    // 更新当前楼层的显示状态
    this.updateLayerVisibilityForCurrentFloor();
  }

  /**
   * 绘制多边形
   */
  drawPolygon(
    positions: Array<{ longitude: number; latitude: number }>,
    options: any = {},
  ): void {
    if (!MAP_UTIL.isReady()) return;

    const polygonId = `${options.layerKey}_${options.id}`;

    // 确定图层key，如果没有指定，使用默认的'polygons'图层
    const layerKey = options.layerKey || "polygons";

    // 检查多边形是否已经存在（在当前楼层中检查）
    if (this.vectorLayers[this.currentFloor]) {
      const existingPolygon = this.vectorLayers[this.currentFloor].find(
        (item: any) => item.id === polygonId,
      );
      if (existingPolygon) {
        console.log(`多边形 ${polygonId} 已存在，跳过绘制`);
        return;
      }
    }

    // 将多边形信息存储到当前楼层对应的图层中
    if (!this.vectorLayers[this.currentFloor]) {
      this.vectorLayers[this.currentFloor] = [];
    }

    const polygonData = {
      id: polygonId,
      positions,
      options,
      floor: options.floor,
      layerKey: layerKey, // 保存图层key，用于后续识别属于哪个图层
      type: "polygon",
    };

    this.vectorLayers[this.currentFloor].push(polygonData);

    // 绘制多边形并保存实体引用
    const entity = MAP_UTIL.drawPolygon(positions, {
      id: polygonId,
      fillColor: options.fillColor || "#409eff",
      fillAlpha: options.fillAlpha || 0.2,
      outlineColor: options.outlineColor || "#409eff",
      outlineWidth: options.outlineWidth || 2,
      label: options.label || "",
      description: options.description || "",
      showLabel: options.showLabel || false,
      onClick: (entity) => {
        // 调用点位点击回调
        if (this.clickCallbacks.onPointClick) {
          this.clickCallbacks.onPointClick(options, entity);
        }

        // 如果options中有onClick回调，直接调用它
        if (options.onClick && typeof options.onClick === "function") {
          options.onClick(entity);
        }
      },
    });

    // 保存实体引用以便后续控制显隐
    if (entity) {
      const pointIndex = this.vectorLayers[options.floor]?.findIndex(
        (item: any) => item.id === polygonId,
      );
      if (pointIndex !== -1) {
        this.vectorLayers[options.floor][pointIndex].entity = entity;
      }
    }
  }

  /**
   * 移除多边形
   */
  removePolygon(polygonId: string): void {
    if (!MAP_UTIL.isReady()) return;
    MAP_UTIL.removePolygon(polygonId);
  }

  /**
   * 清除所有故障点位图层 - 只清除故障点位，保留其他点位
   */
  clearFaultPointsLayer(): void {
    if (!MAP_UTIL.isReady()) return;

    console.log("开始清除所有故障点位（只清除故障点位）");
    let totalCleared = 0;

    // 收集所有需要清除的故障点位信息
    const faultPointsToRemove: Array<{
      id: string;
      entity: any;
      floor: string;
    }> = [];

    // 遍历所有楼层的矢量图层
    Object.keys(this.vectorLayers).forEach((floorName) => {
      if (this.vectorLayers[floorName]) {
        // 找到所有故障点位并收集信息
        this.vectorLayers[floorName].forEach((item: any) => {
          if (item.layerKey === "faultPoints") {
            faultPointsToRemove.push({
              id: item.id,
              entity: item.entity,
              floor: floorName,
            });
            totalCleared++;
          }
        });

        // 从数组中移除故障点位数据
        this.vectorLayers[floorName] = this.vectorLayers[floorName].filter(
          (item: any) => {
            return item.layerKey !== "faultPoints";
          },
        );
      }
    });

    // 逐个清除故障点位
    faultPointsToRemove.forEach((faultPoint) => {
      try {
        console.log(
          `清除故障点位: ${faultPoint.id} (楼层: ${faultPoint.floor})`,
        );

        // 1. 通过实体引用隐藏
        if (faultPoint.entity) {
          this.hideLayerItem({ entity: faultPoint.entity, id: faultPoint.id });
        }

        // 2. 通过MAP_UTIL移除
        if (MAP_UTIL.removePoint) {
          MAP_UTIL.removePoint(faultPoint.id);
        }

        // 3. 直接从MAP_UTIL实体中删除
        if (
          MAP_UTIL.entities &&
          MAP_UTIL.entities.points &&
          MAP_UTIL.entities.points[faultPoint.id]
        ) {
          delete MAP_UTIL.entities.points[faultPoint.id];
        }
      } catch (e) {
        console.warn(`清除故障点位 ${faultPoint.id} 失败:`, e);
      }
    });

    console.log(`✅ 成功清除 ${totalCleared} 个故障点位，保留其他点位不受影响`);
  }

  /**
   * 飞行到指定位置
   * @param position 目标位置 {longitude, latitude}
   * @param options 飞行选项
   */
  flyTo(
    position: { longitude: number; latitude: number },
    options: {
      zoom?: number;
      duration?: number;
      easing?: string;
    } = {},
  ): void {
    if (!MAP_UTIL.isReady()) return;

    const map = MAP_UTIL.getMap();
    if (!map) return;

    const { zoom, duration = 1.5, easing = "easeInOutCubic" } = options;

    // 如果指定了缩放级别，则飞行到指定位置和缩放级别
    if (zoom !== undefined) {
      map.flyTo([position.latitude, position.longitude], zoom, {
        duration: duration,
        easeLinearity: 0.25,
        easing: easing,
      });
    } else {
      // 否则只飞行到指定位置，保持当前缩放级别
      map.flyTo([position.latitude, position.longitude], map.getZoom(), {
        duration: duration,
        easeLinearity: 0.25,
      });
    }
  }

  /**
   * 距离测量
   * @param onResult 测量结果回调函数
   * @returns 返回1表示成功启动测量，0表示失败
   */
  measureDistance(onResult?: (distance: number, layer: any) => void): number {
    if (!MAP_UTIL.isReady()) return 0;

    try {
      const map = MAP_UTIL.getMap();
      if (!map) return 0;
      this.drawLineRealTime(onResult);
      return 1; // 返回成功状态
    } catch (error) {
      console.error("距离测量失败:", error);
      return 0;
    }
  }

  /**
   * 面积测量
   * @param onResult 测量结果回调函数
   * @returns 返回1表示成功启动测量，0表示失败
   */
  measureArea(onResult?: (area: number, layer: any) => void): number {
    if (!MAP_UTIL.isReady()) return 0;

    try {
      const map = MAP_UTIL.getMap();
      if (!map) return 0;
      this.drawPolygonRealTime(onResult);
      return 1; // 返回成功状态
    } catch (error) {
      console.error("面积测量失败:", error);
      return 0;
    }
  }

  /**
   * 测量区域清除
   */
  clearMeasure(): void {
    if (!MAP_UTIL.isReady()) return;

    try {
      const map = MAP_UTIL.getMap();
      if (!map) return;

      // 清除所有测量结果
      if (this.measureResults && this.measureResults.length > 0) {
        this.measureResults.forEach((result: any) => {
          if (result.layer && map.hasLayer) {
            if (map.hasLayer(result.layer)) {
              map.removeLayer(result.layer);
            }
          }
        });
        this.measureResults = [];
      }

      this.stopDrawing();

      map.off("contextmenu");
    } catch (error) {
      console.error("清除测量失败:", error);
    }
  }

  /**
   * 创建真正的热力图图层
   * @param layerId 图层ID
   * @param options 热力图配置
   * @returns 热力图图层对象
   */
  createHeatmapLayer(layerId: string, options: HeatmapOptions = {}): any {
    if (!MAP_UTIL.isReady()) return null;
    return MAP_UTIL.createHeatmapLayer(layerId, options);
  }

  /**
   * 添加热力图数据
   * @param layerId 图层ID
   * @param data 热力图数据
   */
  addHeatmapData(layerId: string, data: HeatmapData[]): void {
    if (!MAP_UTIL.isReady()) return;
    console.log("addHeatmapData热力图数据", layerId, data);
    MAP_UTIL.addHeatmapData(layerId, data);
  }

  /**
   * 移除所有热力图图层
   */
  removeAllHeatmapLayers(): void {
    if (!MAP_UTIL.isReady()) return;
    MAP_UTIL.removeAllHeatmapLayers();
  }

  /**
   * 移除热力图图层
   */
  removeHeatmapLayer(layerId: string): void {
    if (!MAP_UTIL.isReady()) return;
    MAP_UTIL.removeHeatmapLayer(layerId);
  }

  /**
   * 获取热力图图层
   * @param layerId 图层ID
   * @returns 热力图图层对象
   */
  getHeatmapLayer(layerId: string): any {
    if (!MAP_UTIL.isReady()) return null;
    return MAP_UTIL.getHeatmapLayer(layerId);
  }

  /**
   * 获取所有热力图图层
   * @returns 热力图图层对象数组
   */
  getAllHeatmapLayers(): any[] {
    if (!MAP_UTIL.isReady()) return [];
    return MAP_UTIL.getAllHeatmapLayers();
  }

  /**
   * 绘制环境监测多边形
   */
  drawEnvironmentPolygons(): void {
    if (!MAP_UTIL.isReady() || this.environmentRegionsData.length === 0) return;

    this.removeEnvironmentPolygons();

    this.environmentRegionsData.forEach((region) => {
      if (!region.coordinates) return;

      const color = this.getRegionColor(region);

      const positions = region.coordinates.map((coord) => ({
        longitude: coord[1],
        latitude: coord[0],
      }));

      const polygonId = region.id || `env_${Math.random()}`;
      MAP_UTIL.drawPolygon(positions, {
        id: polygonId,
        fillColor: color,
        fillAlpha: 0.2,
        outlineColor: color,
        outlineWidth: 2,
        label: `温度: ${region.temperature}°C\n湿度: ${region.humidity}%\n照明: ${region.lighting}lx`,
        showLabel: false, // 不显示标签
        description: `环境区域_${region.id}`,
        onClick: (entity) => {
          // 调用多边形点击回调
          if (this.clickCallbacks.onPolygonClick) {
            this.clickCallbacks.onPolygonClick(region, entity);
          }

          const bounds = L.latLngBounds(region.coordinates);
          const center = bounds.getCenter();
          MAP_UTIL.setCenter([center.lat, center.lng]);
          console.log("环境区域点击", region);
        },
      });

      // 存储多边形ID用于后续移除
      this.environmentPolygonIds.push(polygonId);

      // 添加数据标签
      const bounds = L.latLngBounds(region.coordinates);
      const center = bounds.getCenter();

      const dataContent = `
        <div style="
          font-size: 12px;
          line-height: 30px;
          text-align: center;
          min-width: 80px;
          color: ${color};
          font-weight: 500;
        ">
          <div>温度: ${region.temperature}°C</div>
          <div>湿度: ${region.humidity}%</div>
          <div>照明: ${region.lighting}lx</div>
        </div>
      `;

      const dataLabel = L.marker(center, {
        icon: L.divIcon({
          html: dataContent,
          className: "environment-data-label",
          iconSize: [120, 90],
          iconAnchor: [60, 45],
        }),
      });

      dataLabel.addTo(MAP_UTIL.getMap());

      const handleClick = () => {
        // 调用多边形点击回调
        if (this.clickCallbacks.onPolygonClick) {
          this.clickCallbacks.onPolygonClick(region, dataLabel);
        }

        MAP_UTIL.setCenter([center.lat, center.lng]);
        console.log("环境数据标签点击", region);
      };

      dataLabel.on("click", handleClick);
      this.environmentPolygons.push(dataLabel);
    });

    console.log(`已绘制 ${this.environmentPolygons.length} 个环境监测多边形`);
  }

  /**
   * 移除环境监测多边形
   */
  removeEnvironmentPolygons(): void {
    if (MAP_UTIL.isReady()) {
      // 移除通过 MAP_UTIL.drawPolygon 绘制的多边形
      if (this.environmentPolygonIds.length > 0) {
        this.environmentPolygonIds.forEach((polygonId) => {
          MAP_UTIL.removePolygon(polygonId);
        });
        this.environmentPolygonIds = [];
      }

      // 移除数据标签
      if (this.environmentPolygons.length > 0) {
        this.environmentPolygons.forEach((polygon) => {
          MAP_UTIL.getMap().removeLayer(polygon);
        });
        this.environmentPolygons = [];
      }

      console.log("环境监测多边形已移除");
    }
  }

  /**
   * 根据状态值获取状态
   */
  private getStatus(
    value: number,
    type: "temperature" | "humidity" | "lighting",
  ): string {
    if (type === "temperature") {
      if (value > 30) return "high";
      if (value > 20) return "normal";
      return "low";
    } else if (type === "humidity") {
      if (value > 60) return "high";
      if (value > 40) return "normal";
      return "low";
    } else if (type === "lighting") {
      if (value > 500) return "high";
      if (value > 200) return "normal";
      return "low";
    }
    return "normal";
  }

  /**
   * 根据区域综合状态获取矩形颜色
   */
  private getRegionColor(region: EnvironmentRegion): string {
    const tempStatus = this.getStatus(region.temperature, "temperature");
    const humidityStatus = this.getStatus(region.humidity, "humidity");
    const lightingStatus = this.getStatus(region.lighting, "lighting");

    if (
      tempStatus === "high" ||
      humidityStatus === "high" ||
      lightingStatus === "high"
    ) {
      return "#e34d59"; // 红色
    }

    if (
      tempStatus === "normal" &&
      humidityStatus === "normal" &&
      lightingStatus === "normal"
    ) {
      return "#ff9800"; // 橙色
    }

    return "#409eff"; // 蓝色
  }

  /**
   * 获取当前楼层
   */
  getCurrentFloor(): string {
    return this.currentFloor;
  }

  /**
   * 获取地图加载状态
   */
  getMapLoadingState(): boolean {
    return this.isMapLoading;
  }

  /**
   * 获取地图就绪状态
   */
  getMapReadyState(): boolean {
    return this.isHomeMapReady;
  }

  /**
   * 获取地图实例
   */
  getMap(): any {
    return MAP_UTIL.getMap();
  }

  /**
   * 获取矢量图层数据
   */
  getVectorLayers(): Record<string, any> {
    return this.vectorLayers;
  }

  /**
   * 设置点击回调
   */
  setClickCallbacks(callbacks: {
    onPointClick?: (pointData: any, entity: any) => void;
    onPolygonClick?: (polygonData: any, entity: any) => void;
    onLineClick?: (lineData: any, entity: any) => void;
  }): void {
    // 合并回调而不是覆盖，这样可以同时支持多个组件的回调
    this.clickCallbacks = {
      ...this.clickCallbacks,
      ...callbacks,
    };
  }

  /**
   * 销毁方法
   */
  destroy(): void {
    this.removeEnvironmentPolygons();
    this.removeAllHeatmapLayers();
    this.clearMeasure(); // 清除测量结果
    this.stopDrawing(); // 停止绘制
    MAP_UTIL.removeAllVectorLayers();
    MAP_UTIL.destroy();

    this.isHomeMapReady = false;
    this.isMapLoading = false;
    this.currentFloor = "F1";
    this.vectorLayers = {};
    this.environmentPolygons = [];
    this.environmentPolygonIds = [];
    this.environmentRegionsData = [];
    this.measureResults = []; // 清空测量结果
    this.isDrawing = false;
    this.currentDrawingType = null;
    this.drawingEventHandlers = null;
  }

  /**
   * 实时绘制线
   * @param onResult 绘制完成后的回调函数，返回距离结果
   */
  drawLineRealTime(onResult?: (distance: number, layer: any) => void): void {
    if (!MAP_UTIL.isReady()) return;

    try {
      const map = MAP_UTIL.getMap();
      if (!map) return;

      // 如果已经在绘制中，先停止
      if (this.isDrawing) {
        this.stopDrawing();
      }

      this.isDrawing = true;
      this.currentDrawingType = "line";

      // 创建绘制状态变量
      let isDrawing = false;
      let currentLine: any = null;
      let linePoints: number[][] = [];
      let tempLine: any = null;
      let pointMarkers: any[] = [];

      // 鼠标移动事件处理
      const onMouseMove = (e: any) => {
        if (!isDrawing || !currentLine) return;

        const latlng = e.latlng;

        // 更新临时线段的终点
        if (tempLine) {
          const tempPoints = [...linePoints, [latlng.lat, latlng.lng]];
          tempLine.setLatLngs(tempPoints);
        }
      };

      // 地图点击事件处理
      const onMapClick = (e: any) => {
        const latlng = e.latlng;

        if (!isDrawing) {
          // 开始绘制
          isDrawing = true;
          linePoints = [[latlng.lat, latlng.lng]];

          // 创建主线条
          currentLine = L.polyline(linePoints, {
            color: "#409eff",
            weight: 3,
            opacity: 0.8,
          }).addTo(map);

          // 创建临时跟随线段
          tempLine = L.polyline([...linePoints, [latlng.lat, latlng.lng]], {
            color: "#409eff",
            weight: 2,
            opacity: 0.5,
            dashArray: "5, 5",
          }).addTo(map);

          // 添加起点标记
          const startMarker = L.marker([latlng.lat, latlng.lng], {
            icon: L.divIcon({
              html: '<div style="background: #409eff; width: 8px; height: 8px; border-radius: 50%; border: 1px solid white;"></div>',
              className: "drawing-point-marker",
              iconSize: [8, 8],
              iconAnchor: [4, 4],
            }),
          }).addTo(map);
          pointMarkers.push(startMarker);
        } else {
          // 继续绘制
          linePoints.push([latlng.lat, latlng.lng]);
          currentLine.setLatLngs(linePoints);

          // 添加中间点标记
          const marker = L.marker([latlng.lat, latlng.lng], {
            icon: L.divIcon({
              html: '<div style="background: #409eff; width: 8px; height: 8px; border-radius: 50%; border: 1px solid white;"></div>',
              className: "drawing-point-marker",
              iconSize: [8, 8],
              iconAnchor: [4, 4],
            }),
          }).addTo(map);
          pointMarkers.push(marker);
        }
      };

      // 右键完成绘制
      const onRightClick = (e: any) => {
        if (!isDrawing || !currentLine) return;

        e.originalEvent.preventDefault();

        // 立即停止绘制状态，防止鼠标移动继续更新临时线段
        isDrawing = false;

        // 立即移除临时线段
        if (tempLine && map.hasLayer(tempLine)) {
          map.removeLayer(tempLine);
        }

        // 移除所有节点标记
        pointMarkers.forEach((marker) => {
          if (map.hasLayer(marker)) {
            map.removeLayer(marker);
          }
        });

        // 完成绘制
        this.finishLineDrawing(currentLine, linePoints, onResult);

        // 清理临时状态
        this.cleanupDrawing();
      };

      // 绑定事件
      map.on("click", onMapClick);
      map.on("mousemove", onMouseMove);
      map.on("contextmenu", onRightClick);

      // 存储事件处理器用于后续清理
      this.drawingEventHandlers = {
        map,
        handlers: {
          click: onMapClick,
          mousemove: onMouseMove,
          contextmenu: onRightClick,
        },
        tempLine,
      };
    } catch (error) {
      console.error("实时绘制线失败:", error);
      this.isDrawing = false;
    }
  }

  /**
   * 实时绘制面
   * @param onResult 绘制完成后的回调函数，返回面积结果
   */
  drawPolygonRealTime(onResult?: (area: number, layer: any) => void): void {
    if (!MAP_UTIL.isReady()) return;

    try {
      const map = MAP_UTIL.getMap();
      if (!map) return;

      // 如果已经在绘制中，先停止
      if (this.isDrawing) {
        this.stopDrawing();
      }

      this.isDrawing = true;
      this.currentDrawingType = "polygon";

      // 创建绘制状态变量
      let isDrawing = false;
      let currentPolygon: any = null;
      let polygonPoints: number[][] = [];
      let tempLine: any = null;
      let pointMarkers: any[] = [];

      // 鼠标移动事件处理
      const onMouseMove = (e: any) => {
        if (!isDrawing || polygonPoints.length === 0) return;

        const latlng = e.latlng;

        // 更新临时线段的终点，连接到起点形成闭合
        if (tempLine && polygonPoints.length > 0) {
          const tempPoints = [
            ...polygonPoints,
            [latlng.lat, latlng.lng],
            polygonPoints[0],
          ];
          tempLine.setLatLngs(tempPoints);
        }
      };

      // 地图点击事件处理
      const onMapClick = (e: any) => {
        const latlng = e.latlng;

        if (!isDrawing) {
          // 开始绘制
          isDrawing = true;
          polygonPoints = [[latlng.lat, latlng.lng]];

          // 创建主多边形
          currentPolygon = L.polygon([polygonPoints], {
            color: "#409eff",
            weight: 3,
            opacity: 0.8,
            fillColor: "#409eff",
            fillOpacity: 0.2,
          }).addTo(map);

          // 创建临时跟随线段
          tempLine = L.polyline(
            [...polygonPoints, [latlng.lat, latlng.lng], polygonPoints[0]],
            {
              color: "#409eff",
              weight: 2,
              opacity: 0.5,
              dashArray: "5, 5",
            },
          ).addTo(map);

          // 添加起点标记
          const startMarker = L.marker([latlng.lat, latlng.lng], {
            icon: L.divIcon({
              html: '<div style="background: #409eff; width: 8px; height: 8px; border-radius: 50%; border: 1px solid white;"></div>',
              className: "drawing-point-marker",
              iconSize: [8, 8],
              iconAnchor: [4, 4],
            }),
          }).addTo(map);
          pointMarkers.push(startMarker);
        } else {
          // 继续绘制
          polygonPoints.push([latlng.lat, latlng.lng]);
          currentPolygon.setLatLngs([polygonPoints]);

          // 添加中间点标记
          const marker = L.marker([latlng.lat, latlng.lng], {
            icon: L.divIcon({
              html: '<div style="background: #409eff; width: 8px; height: 8px; border-radius: 50%; border: 1px solid white;"></div>',
              className: "drawing-point-marker",
            }),
          }).addTo(map);
          pointMarkers.push(marker);

          // 更新临时线段
          if (tempLine && polygonPoints.length > 0) {
            const tempPoints = [...polygonPoints, polygonPoints[0]];
            tempLine.setLatLngs(tempPoints);
          }

          // 更新临时线段的tempLine引用
          if (this.drawingEventHandlers) {
            this.drawingEventHandlers.tempLine = tempLine;
          }
        }
      };

      // 右键完成绘制
      const onRightClick = (e: any) => {
        if (!isDrawing || !currentPolygon || polygonPoints.length < 3) return;

        e.originalEvent.preventDefault();

        // 立即停止绘制状态，防止鼠标移动继续更新临时线段
        isDrawing = false;

        // 立即移除临时线段
        if (tempLine && map.hasLayer(tempLine)) {
          map.removeLayer(tempLine);
        }

        // 完成绘制
        this.finishPolygonDrawing(currentPolygon, polygonPoints, onResult);

        // 清理临时状态
        this.cleanupDrawing();
      };

      // 绑定事件
      map.on("click", onMapClick);
      map.on("mousemove", onMouseMove);
      map.on("contextmenu", onRightClick);

      // 存储事件处理器用于后续清理
      this.drawingEventHandlers = {
        map,
        handlers: {
          click: onMapClick,
          mousemove: onMouseMove,
          contextmenu: onRightClick,
        },
        markers: pointMarkers,
      };
    } catch (error) {
      console.error("实时绘制面失败:", error);
      this.isDrawing = false;
    }
  }

  /**
   * 停止绘制
   */
  stopDrawing(): void {
    if (!this.isDrawing) return;

    this.isDrawing = false;
    this.currentDrawingType = null;
    this.cleanupDrawing();
  }

  /**
   * 清理绘制状态
   */
  private cleanupDrawing(): void {
    if (this.drawingEventHandlers) {
      const { map, handlers, markers } = this.drawingEventHandlers;

      // 移除事件监听器
      Object.entries(handlers).forEach(([event, handler]) => {
        if (map.off) {
          map.off(event, handler);
        }
      });

      // 移除临时标记
      if (markers) {
        markers.forEach((marker) => {
          if (map.hasLayer(marker)) {
            map.removeLayer(marker);
          }
        });
      }

      this.drawingEventHandlers = null;
    }
  }

  /**
   * 完成线条绘制
   */
  private finishLineDrawing(
    line: any,
    points: number[][],
    onResult?: (distance: number, layer: any) => void,
  ): void {
    if (!MAP_UTIL.isReady()) return;

    try {
      // 计算线条长度
      let totalDistance = 0;
      for (let i = 1; i < points.length; i++) {
        const prev = L.latLng(points[i - 1][0], points[i - 1][1]);
        const curr = L.latLng(points[i][0], points[i][1]);
        totalDistance += prev.distanceTo(curr);
      }

      // 添加测量结果
      this.measureResults.push({
        type: "distance",
        layer: line,
        timestamp: Date.now(),
      });

      // 调用回调函数返回结果
      if (onResult && typeof onResult === "function") {
        onResult(totalDistance, line);
      }

      // 重新开始绘制，实现连续绘制功能
      setTimeout(() => {
        this.drawLineRealTime(onResult);
      }, 100);
    } catch (error) {
      console.error("完成线条绘制失败:", error);
    }
  }

  /**
   * 完成多边形绘制
   */
  private finishPolygonDrawing(
    polygon: any,
    points: number[][],
    onResult?: (area: number, layer: any) => void,
  ): void {
    if (!MAP_UTIL.isReady()) return;

    try {

      // 计算多边形面积 - 使用自定义计算函数替代 L.GeometryUtil.geodesicArea
      const area = this.calculatePolygonArea(points);

      // 添加测量结果
      this.measureResults.push({
        type: "distance",
        layer: polygon,
        timestamp: Date.now(),
      });

      // 调用回调函数返回结果
      if (onResult && typeof onResult === "function") {
        onResult(area, polygon);
      }

      // 重新开始绘制，实现连续绘制功能
      setTimeout(() => {
        this.drawPolygonRealTime(onResult);
      }, 100);
    } catch (error) {
      console.error("完成多边形绘制失败:", error);
    }
  }
  /**
   * 计算多边形面积（平方米）
   * 使用球面三角形公式计算地理坐标下的面积
   * @param points 多边形顶点坐标数组 [[lat, lng], [lat, lng], ...]
   * @returns 面积（平方米）
   */
  private calculatePolygonArea(points: number[][]): number {
    if (points.length < 3) return 0;

    // 地球半径（米）
    const EARTH_RADIUS = 6371393;

    let area = 0;
    const n = points.length;

    for (let i = 0; i < n; i++) {
      const j = (i + 1) % n;
      const xi = (points[i][1] * Math.PI) / 180; // 经度转弧度
      const yi = (points[i][0] * Math.PI) / 180; // 纬度转弧度
      const xj = (points[j][1] * Math.PI) / 180; // 经度转弧度
      const yj = (points[j][0] * Math.PI) / 180; // 纬度转弧度

      // 使用球面三角形面积公式
      area += (xj - xi) * (2 + Math.sin(yi) + Math.sin(yj));
    }

    area = (Math.abs(area) * EARTH_RADIUS * EARTH_RADIUS) / 2;

    return area;
  }
}

// 创建单例实例
export const homeMap = new HomeMap();

// 导出工具函数
export const createHomeMap = () => new HomeMap();
