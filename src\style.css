/* 重置样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

#app {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}

/* 确保地图容器有正确的尺寸 */
.home-map-div {
  width: 100% !important;
  height: 100% !important;
  position: relative;
}

/* Leaflet 地图样式修复 */
.leaflet-container {
  width: 100% !important;
  height: 100% !important;
  background: #f0f0f0;
}

/* 确保地图瓦片正确显示 */
.leaflet-tile-pane {
  opacity: 1 !important;
}

.leaflet-layer {
  opacity: 1 !important;
}