<script setup>
  import { ref, onMounted } from 'vue'
  import L from 'leaflet'
  import 'leaflet-rotate' // 导入旋转插件
  import { TiledMapLayer } from '@supermapgis/iclient-leaflet'
  
  const urlList = ref([
    'https://iserver.supermap.io/iserver/services/map-china400/rest/maps/China',
  ])
  
  const map = ref()
  
  function initMap() {
    console.log('初始化')
    map.value = L.map('map', {
      center: [35.25, 102.55],
      zoom: 4,
      rotate: true,
      attributionControl: false,
      zoomControl: false,
      dragging: true,
      touchZoom: true,
      scrollWheelZoom: true,
      doubleClickZoom: true,
      boxZoom: true,
      keyboard: true,
      bearing: 35, // 初始旋转35度
    })
  
    const url = urlList.value[0]
    new TiledMapLayer(url).addTo(map.value)
  
    // ===== 新增：创建一个 no-rotate 的 pane，用来放文字 =====
    const noRotatePane = map.value.createPane('no-rotate')
    noRotatePane.style.zIndex = 650 // 比瓦片层高一点
    noRotatePane.style.transform = 'none' // 禁止旋转
  }
  onMounted(() => {
    initMap()
  })
  </script>
  
  <template>
    <div id="map" class="home-map-div"></div>
  </template>
  
  <style scoped>
  .home-map-div {
    width: 100%;
    height: 100vh;
  }
  .map-label {
    white-space: nowrap;
    user-select: none;
  }
  </style>
  