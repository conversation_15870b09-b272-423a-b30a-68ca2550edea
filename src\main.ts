import { createApp } from 'vue'
import './style.css'
import App from './App.vue'
// 引入leaflet样式
import 'leaflet/dist/leaflet.css'
// 引入SuperMap iClient样式
import '@supermapgis/iclient-leaflet/dist/iclient-leaflet.min.css'

// 引入leaflet和SuperMap库
import L from 'leaflet'
import '@supermapgis/iclient-leaflet'
import 'leaflet-rotate'

// 将L设置为全局变量
declare global {
  interface Window {
    L: typeof L
  }
}
window.L = L

createApp(App).mount('#app')
