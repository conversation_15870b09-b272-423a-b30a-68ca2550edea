<template>
  <div class="test-map-container">
    <h3>测试地图</h3>
    <div id="test-map" class="test-map-div"></div>
    <div class="debug-info">
      <p>地图状态: {{ mapStatus }}</p>
      <p>错误信息: {{ errorMessage }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import L from 'leaflet'
import { TiledMapLayer } from '@supermapgis/iclient-leaflet'

const mapStatus = ref('未初始化')
const errorMessage = ref('')

onMounted(() => {
  try {
    mapStatus.value = '开始初始化...'
    console.log('开始初始化测试地图')
    console.log('L对象:', L)
    console.log('TiledMapLayer:', TiledMapLayer)

    // 创建基础Leaflet地图
    const map = L.map('test-map', {
      center: [39.9, 116.4], // 北京坐标
      zoom: 10,
      attributionControl: false,
      zoomControl: true,
    })

    console.log('Leaflet地图创建成功:', map)
    mapStatus.value = 'Leaflet地图创建成功'

    // 添加OpenStreetMap瓦片图层作为基础图层
    const osmLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '© OpenStreetMap contributors',
      maxZoom: 19
    })

    osmLayer.on('loading', () => {
      console.log('OSM图层开始加载')
    })

    osmLayer.on('load', () => {
      console.log('OSM图层加载完成')
      mapStatus.value = 'OSM图层加载完成'
    })

    osmLayer.on('tileerror', (error: any) => {
      console.error('OSM瓦片加载错误:', error)
    })

    osmLayer.addTo(map)
    console.log('OSM图层添加到地图')

    // 添加一个测试标记
    const marker = L.marker([39.9, 116.4]).addTo(map)
    marker.bindPopup('这是北京!')
    console.log('测试标记添加成功')

    // 等待一段时间后尝试添加SuperMap图层
    setTimeout(() => {
      try {
        console.log('开始添加SuperMap图层')
        const supermapLayer = new TiledMapLayer('https://iserver.supermap.io/iserver/services/map-china400/rest/maps/China', {
          transparent: true,
        })

        // 监听图层事件
        supermapLayer.on('loading', () => {
          console.log('SuperMap图层开始加载')
          mapStatus.value = 'SuperMap图层加载中...'
        })

        supermapLayer.on('load', () => {
          console.log('SuperMap图层加载完成')
          mapStatus.value = 'SuperMap图层加载完成'
        })

        supermapLayer.on('tileerror', (error: any) => {
          console.error('SuperMap瓦片加载错误:', error)
          errorMessage.value = `SuperMap瓦片错误: ${error.message || error}`
        })

        supermapLayer.on('error', (error: any) => {
          console.error('SuperMap图层加载失败:', error)
          errorMessage.value = `SuperMap图层加载失败: ${error.message || error}`
        })

        supermapLayer.addTo(map)
        console.log('SuperMap图层添加到地图')
        mapStatus.value = 'SuperMap图层已添加'

      } catch (error: any) {
        console.error('创建SuperMap图层失败:', error)
        errorMessage.value = `创建SuperMap图层失败: ${error.message}`
      }
    }, 2000)

  } catch (error: any) {
    console.error('地图初始化失败:', error)
    mapStatus.value = '初始化失败'
    errorMessage.value = error.message
  }
})
</script>

<style scoped>
.test-map-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

h3 {
  margin: 10px;
  color: #333;
}

.test-map-div {
  flex: 1;
  width: 100%;
  min-height: 400px;
  border: 1px solid #ccc;
}

.debug-info {
  padding: 10px;
  background: #f5f5f5;
  border-top: 1px solid #ccc;
}

.debug-info p {
  margin: 5px 0;
  font-size: 14px;
}
</style>
