// 地图工具类
import { h } from 'vue'
import { NButton, NCard, NDescriptions, NDescriptionsItem } from 'naive-ui'
import { TiledMapLayer } from '@supermapgis/iclient-leaflet'

// 声明全局 L 对象
declare const L: any

// 地图配置接口
export interface MapConfig {
  center: [number, number]
  zoom: number
  maxZoom?: number
  minZoom?: number
  attributionControl?: boolean
  logoControl?: boolean
  zoomControl?: boolean
  dragging?: boolean
  touchZoom?: boolean
  scrollWheelZoom?: boolean
  doubleClickZoom?: boolean
  boxZoom?: boolean
  keyboard?: boolean
  rotate?: boolean
  bearing?: number
  backgroundColor?: string
}

// 点数据接口
export interface PointData {
  id: string
  position: [number, number]
  name: string
  floor?: string
  properties?: Record<string, any>
}

// 线数据接口
export interface LineData {
  id: string
  coordinates: [number, number][]
  name: string
  properties?: Record<string, any>
}

// 面数据接口
export interface PolygonData {
  id: string
  coordinates: [number, number][][]
  name: string
  properties?: Record<string, any>
}

// 图层数据接口
export interface LayerData {
  floor: string
  url: string
}

// 弹窗内容接口
export interface PopupContent {
  title: string
  data: Record<string, any>
  actions?: Array<{
    text: string
    type: 'primary' | 'info' | 'success' | 'warning' | 'error'
    onClick?: () => void
  }>
}

// 事件回调接口
export interface MapEventCallbacks {
  onMapClick?: (event: any) => void
  onPointClick?: (point: PointData, event: any) => void
  onLineClick?: (line: LineData, event: any) => void
  onPolygonClick?: (polygon: PolygonData, event: any) => void
  onZoomEnd?: (zoom: number) => void
  onMoveEnd?: (center: [number, number]) => void
  onGroundClick?: (position: { longitude: number, latitude: number }, event: any) => void
}

// 绘制点选项接口
export interface DrawPointOptions {
  id?: string
  iconUrl?: string
  iconSize?: [number, number]
  scale?: number
  color?: string
  pointSize?: number
  label?: string
  description?: string
  clickable?: boolean
  show?: boolean
  showLabel?: boolean
  data?: any
  floor?: string
  layerKey?: string
  onClick?: (entity: any) => void
}

// 绘制面选项接口
export interface DrawPolygonOptions {
  id?: string
  fillColor?: string
  fillAlpha?: number
  outlineColor?: string
  outlineWidth?: number
  outlineAlpha?: number
  materialType?: 'Color' | 'Image' | 'Grid' | 'Stripe' | 'Checkerboard'
  materialParams?: any
  label?: string
  showLabel?: boolean
  description?: string
  clickable?: boolean
  show?: boolean
  data?: any
  floor?: string
  onClick?: (entity: any) => void
}

// 绘制线选项接口
export interface DrawLineOptions {
  id?: string
  color?: string
  width?: number
  opacity?: number
  description?: string
  clickable?: boolean
  show?: boolean
  onClick?: (entity: any) => void
}

// 热力图数据接口
export interface HeatmapData {
  type: 'Feature'
  geometry: {
    type: 'Point'
    coordinates: [number, number]
  }
  properties: {
    value: number
    [key: string]: any
  }
}

// 热力图配置接口
export interface HeatmapOptions {
  id?: string
  radius?: number
  opacity?: number
  maxZoom?: number
  minZoom?: number
  featureWeight?: string
  useGeoUnit?: boolean
  gradient?: Record<string, string>
  blur?: number
  max?: number
  min?: number
}

// 热力图图层接口
export interface HeatmapLayer {
  id: string
  layer: any
  options: HeatmapOptions
  data: HeatmapData[]
}

/**
 * 封装业务侧地图工具类
 */
export const MAP_UTIL = {
  // 地图对象
  map: null as any,
  // 图层对象
  layers: {} as Record<string, any>,
  // 实体对象
  entities: {
    points: {} as Record<string, any>,
    lines: {} as Record<string, any>,
    polygons: {} as Record<string, any>,
  },
  // 热力图图层
  heatmapLayers: {} as Record<string, HeatmapLayer>,
  // 当前楼层
  currentFloor: 'F1',
  // 地图加载状态
  isMapReady: false,
  isMapLoading: true,

  // 内部引用
  _viewer: null as any,
  _dataSource: null as any,
  _mapPickHandler: null as any,

  // 回调函数
  callbacks: {} as MapEventCallbacks,
  groundClickCallback: null as any,
  mapReadyCallback: null as any,
  zoomLimitCallback: null as any,

  // 实体点击回调映射
  _entityClickCallbacks: new Map<string, (entity: any) => void>(),

  // ----------------  地图初始化  ----------------
  /**
   * 初始化地图
   * @param containerId 地图容器ID
   * @param config 地图配置
   * @param layers 图层数据
   * @param callbacks 事件回调
   */
  async initMap(
    containerId: string,
    config: MapConfig,
    layers: LayerData[],
    callbacks: MapEventCallbacks = {}
  ): Promise<any> {
    try {
      this.isMapLoading = true

      // 创建地图实例
      this.map = L.map(containerId, {
        attributionControl: false,
        logoControl: false,
        zoomControl: false,
        crs: L.CRS.EPSG4326,
        maxZoom: config.maxZoom || 18,
        minZoom: config.minZoom || 15,
        dragging: true,
        touchZoom: true,
        scrollWheelZoom: true,
        doubleClickZoom: true,
        boxZoom: true,
        keyboard: true,
        rotate: true,
        reuseTiles: true,
        updateWhenIdle: true,
        ...config
      })

      // 设置地图背景颜色
      const backgroundColor = config.backgroundColor || '#f0f0f0'
      this.map.getContainer().style.backgroundColor = backgroundColor

      // 添加比例尺控件
      // L.control.scale({
      //   position: 'bottomright',
      //   maxWidth: 100,
      //   metric: true,
      //   imperial: false,
      // }).addTo(this.map)

      // 创建图层
      this.createLayers(layers)

      // 设置事件回调
      this.callbacks = callbacks

      // 绑定地图事件
      this.bindMapEvents()

      this.isMapReady = true
      this.isMapLoading = false

      // 触发地图加载完成回调
      if (this.mapReadyCallback) {
        this.mapReadyCallback({ map: this.map, layers: this.layers })
      }

      console.log('地图初始化成功:', {
        center: this.map.getCenter(),
        zoom: this.map.getZoom(),
        size: this.map.getSize(),
        layers: Object.keys(this.layers)
      })

      return this.map
    } catch (error) {
      console.error('地图初始化失败:', error)
      this.isMapLoading = false
      throw error
    }
  },

  /**
   * 创建图层
   * @param layers 图层数据
   */
  createLayers(layers: LayerData[]): void {
    layers.forEach((layer) => {
      this.layers[layer.floor] = new TiledMapLayer(layer.url, {
        transparent: true,
        updateInterval: 300,
        reuseTiles: true,
      })
    })
  },

  /**
   * 绑定地图事件
   */
  bindMapEvents(): void {
    if (!this.map) return

    // 地图点击事件
    this.map.on('click', (event: any) => {
      if (this.callbacks.onMapClick) {
        this.callbacks.onMapClick(event)
      }

      // 触发地面点击回调
      if (this.groundClickCallback) {
        const latlng = event.latlng
        this.groundClickCallback({
          longitude: latlng.lng,
          latitude: latlng.lat
        }, event)
      }
    })

    // 缩放结束事件
    this.map.on('zoomend', () => {
      const currentZoom = this.map.getZoom()
      const maxZoom = this.map.getMaxZoom()
      const minZoom = this.map.getMinZoom()
      
      // 检查是否达到缩放边界
      if (currentZoom >= maxZoom) {
        console.log('已达到最大缩放级别')
        this.showMessage('已达到最大缩放级别')
      } else if (currentZoom <= minZoom) {
        this.showMessage('已达到最小缩放级别')
      }
      
      if (this.callbacks.onZoomEnd) {
        this.callbacks.onZoomEnd(currentZoom)
      }
    })

    // 移动结束事件
    this.map.on('moveend', () => {
      if (this.callbacks.onMoveEnd) {
        this.callbacks.onMoveEnd([
          this.map.getCenter().lat,
          this.map.getCenter().lng
        ])
      }
    })
  },

  // ----------------  图层管理  ----------------
  /**
   * 切换图层
   * @param floorName 楼层名称
   */
  switchLayer(floorName: string): void {
    if (!this.map || !this.layers[floorName]) return

    // 移除所有图层
    Object.values(this.layers).forEach((layer) => {
      if (layer) {
        this.map.removeLayer(layer)
      }
    })

    // 添加指定图层
    this.layers[floorName].addTo(this.map)
    this.currentFloor = floorName
  },

  /**
   * 获取当前楼层
   */
  getCurrentFloor(): string {
    return this.currentFloor
  },

  // ----------------  绘制功能  ----------------
  /**
   * 绘制点
   * @param position 点位信息 {longitude, latitude}
   * @param options 绘制选项
   * @returns 创建的标记对象
   */
  drawPoint(position: { longitude: number, latitude: number }, options: DrawPointOptions = {}): any {
    if (!this.map) {
      console.error('地图未初始化，无法绘制点')
      return null
    }

    try {
      // 计算缩放后的图标大小
      const baseIconSize = options.iconSize || [32, 32]
      const scale = options.scale || 1
      const scaledIconSize: [number, number] = [
        Math.round(baseIconSize[0] * scale),
        Math.round(baseIconSize[1] * scale)
      ]
      
      // 计算缩放后的锚点位置
      const baseIconAnchor = [22, 22]
      const scaledIconAnchor: [number, number] = [
        Math.round(baseIconAnchor[0] * scale),
        Math.round(baseIconAnchor[1] * scale)
      ]
      
      // 计算缩放后的弹窗锚点位置
      const basePopupAnchor = [-3, -20]
      const scaledPopupAnchor: [number, number] = [
        Math.round(basePopupAnchor[0] * scale),
        Math.round(basePopupAnchor[1] * scale)
      ]

      const icon = L.icon({
        iconUrl: options.iconUrl ?? new URL('@@/assets/images/home/<USER>', import.meta.url).href,
        iconSize: scaledIconSize,
        iconAnchor: scaledIconAnchor,
        popupAnchor: scaledPopupAnchor,
      })

      const marker = L.marker([position.latitude, position.longitude], { icon })

      // 绑定点击事件 - 不显示弹窗，只执行回调
      if (options.onClick) {
        marker.on('click', (event: any) => {
          // 阻止默认的弹窗行为
          event.originalEvent?.preventDefault?.()
          event.originalEvent?.stopPropagation?.()
          
          // 执行自定义回调
          options.onClick!(marker)
        })
      }

      // 设置标签 - 只在明确要求显示时显示
      if (options.label && options.showLabel === true) {
        const label = L.tooltip({
          permanent: true,
          direction: 'bottom',
          className: 'point-label'
        }).setContent(options.label)
        marker.bindTooltip(label)
      }

      marker.addTo(this.map)
      
      const entityId = options.id || `point_${Date.now()}_${Math.random()}`
      this.entities.points[entityId] = marker

      return marker
    } catch (error) {
      console.error('绘制点失败:', error)
      return null
    }
  },

  /**
   * 绘制面
   * @param positions 面顶点坐标数组
   * @param options 绘制选项
   * @returns 创建的多边形对象
   */
  drawPolygon(positions: Array<{ longitude: number, latitude: number }>, options: DrawPolygonOptions = {}): any {
    if (!this.map) {
      console.error('地图未初始化，无法绘制面')
      return null
    }

    try {
      const latlngs = positions.map(pos => [pos.latitude, pos.longitude])
      
      const polygon = L.polygon(latlngs, {
        color: options.outlineColor || '#409eff',
        weight: options.outlineWidth || 2,
        opacity: options.outlineAlpha || 0.8,
        fillColor: options.fillColor || '#409eff',
        fillOpacity: options.fillAlpha || 0.2,
      })

      // 绑定点击事件 - 不显示弹窗，只执行回调
      if (options.onClick) {
        polygon.on('click', (event: any) => {
          // 阻止默认的弹窗行为
          event.originalEvent?.preventDefault?.()
          event.originalEvent?.stopPropagation?.()
          
          // 执行自定义回调
          options.onClick!(polygon)
        })
      }

      // 设置标签
      if (options.label && options.showLabel !== false) {
        const center = this.calculatePolygonCenter(positions)
        const label = L.marker([center.latitude, center.longitude], {
          icon: L.divIcon({
            html: `<div style="background: rgba(0,0,0,0.7); color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px;">${options.label}</div>`,
            className: 'polygon-label',
            iconSize: [options.label.length * 8, 20],
            iconAnchor: [options.label.length * 4, 10]
          })
        })
        label.addTo(this.map)
        this.entities.polygons[`label_${options.id || Date.now()}`] = label
      }

      polygon.addTo(this.map)
      
      const entityId = options.id || `polygon_${Date.now()}`
      this.entities.polygons[entityId] = polygon

      return polygon
    } catch (error) {
      console.error('绘制面失败:', error)
      return null
    }
  },

  /**
   * 绘制线
   * @param positions 线顶点坐标数组
   * @param options 绘制选项
   * @returns 创建的线对象
   */
  drawLine(positions: Array<{ longitude: number, latitude: number }>, options: DrawLineOptions = {}): any {
    if (!this.map) {
      console.error('地图未初始化，无法绘制线')
      return null
    }

    try {
      const latlngs = positions.map(pos => [pos.latitude, pos.longitude])
      
      const polyline = L.polyline(latlngs, {
        color: options.color || '#409eff',
        weight: options.width || 3,
        opacity: options.opacity || 0.8,
      })

      // 绑定点击事件 - 不显示弹窗，只执行回调
      if (options.onClick) {
        polyline.on('click', (event: any) => {
          // 阻止默认的弹窗行为
          event.originalEvent?.preventDefault?.()
          event.originalEvent?.stopPropagation?.()
          
          // 执行自定义回调
          options.onClick!(polyline)
        })
      }

      polyline.addTo(this.map)
      
      const entityId = options.id || `line_${Date.now()}`
      this.entities.lines[entityId] = polyline

      return polyline
    } catch (error) {
      console.error('绘制线失败:', error)
      return null
    }
  },

  /**
   * 计算多边形中心点
   */
  calculatePolygonCenter(positions: Array<{ longitude: number, latitude: number }>) {
    const center = positions.reduce(
      (acc, pos) => ({
        longitude: acc.longitude + pos.longitude,
        latitude: acc.latitude + pos.latitude,
      }),
      { longitude: 0, latitude: 0 }
    )

    return {
      longitude: center.longitude / positions.length,
      latitude: center.latitude / positions.length,
    }
  },

  /**
   * 创建弹窗内容
   */
  createPopupContent(content: PopupContent) {
    const descriptionItems = Object.entries(content.data).map(([key, value]) =>
      h(NDescriptionsItem, { label: key }, () => value)
    )

    const actions = content.actions?.map(action =>
      h(NButton, {
        type: action.type,
        size: 'small',
        onClick: action.onClick
      }, () => action.text)
    ) || []

    return h(NCard, { class: 'custom-popup' }, {
      header: () => h('span', { class: 'popup-title' }, content.title),
      default: () => [
        h(NDescriptions, { bordered: true, column: 2, labelPlacement: 'left' }, {
          default: () => descriptionItems,
        }),
        h('div', { class: 'popup-footer' }, actions),
      ],
    })
  },

  // ----------------  热力图管理  ----------------
  /**
   * 创建热力图图层
   * @param id 图层ID
   * @param options 热力图配置
   * @returns 热力图图层对象
   */
  createHeatmapLayer(id: string, options: HeatmapOptions = {}): HeatmapLayer {
    if (!this.map) {
      console.error('地图未初始化，无法创建热力图图层')
      return null as any
    }

    try {
      // 默认配置
      const defaultOptions: HeatmapOptions = {
        radius: 45,
        opacity: 0.8,
        maxZoom: 18,
        minZoom: 0,
        featureWeight: 'value',
        useGeoUnit: false,
        gradient: {
          '0.0': '#0000ff',
          '0.25': '#00ffff',
          '0.5': '#00ff00',
          '0.75': '#ffff00',
          '1.0': '#ff0000'
        },
        blur: 15,
        max: 10,
        min: 0
      }

      const heatmapOptions = { ...defaultOptions, ...options }

      // 创建热力图图层
      const heatmapLayer = new (L as any).supermap.HeatMapLayer(
        id,
        {
          map: this.map,
          id: id,
          radius: heatmapOptions.radius,
          opacity: heatmapOptions.opacity,
          maxZoom: heatmapOptions.maxZoom,
          minZoom: heatmapOptions.minZoom,
          featureWeight: heatmapOptions.featureWeight,
          useGeoUnit: heatmapOptions.useGeoUnit,
          gradient: heatmapOptions.gradient,
          blur: heatmapOptions.blur,
          max: heatmapOptions.max,
          min: heatmapOptions.min
        }
      )

      const heatmapLayerObj: HeatmapLayer = {
        id,
        layer: heatmapLayer,
        options: heatmapOptions,
        data: []
      }

      this.heatmapLayers[id] = heatmapLayerObj
      return heatmapLayerObj
    } catch (error) {
      console.error('创建热力图图层失败:', error)
      return null as any
    }
  },

  /**
   * 添加热力图数据
   * @param layerId 图层ID
   * @param data 热力图数据
   */
  addHeatmapData(layerId: string, data: HeatmapData[]): void {
    if (!this.heatmapLayers[layerId]) {
      console.error(`热力图图层不存在: ${layerId}`)
      return
    }

    try {
      const heatmapLayer = this.heatmapLayers[layerId]
      
      // 转换为FeatureCollection格式
      const featureCollection = {
        type: 'FeatureCollection',
        features: data
      }

      // 添加数据到热力图图层
      heatmapLayer.layer.addFeatures(featureCollection)
      heatmapLayer.data = [...heatmapLayer.data, ...data]

      // 将图层添加到地图
      if (!this.map.hasLayer(heatmapLayer.layer)) {
        this.map.addLayer(heatmapLayer.layer)
      }

      console.log(`热力图数据添加成功，图层: ${layerId}，数据点: ${data.length}`)
    } catch (error) {
      console.error('添加热力图数据失败:', error)
    }
  },

  /**
   * 更新热力图数据
   * @param layerId 图层ID
   * @param data 新的热力图数据
   */
  updateHeatmapData(layerId: string, data: HeatmapData[]): void {
    if (!this.heatmapLayers[layerId]) {
      console.error(`热力图图层不存在: ${layerId}`)
      return
    }

    try {
      const heatmapLayer = this.heatmapLayers[layerId]
      
      // 清除现有数据
      heatmapLayer.layer.clearFeatures()
      
      // 添加新数据
      const featureCollection = {
        type: 'FeatureCollection',
        features: data
      }

      heatmapLayer.layer.addFeatures(featureCollection)
      heatmapLayer.data = data

      console.log(`热力图数据更新成功，图层: ${layerId}，数据点: ${data.length}`)
    } catch (error) {
      console.error('更新热力图数据失败:', error)
    }
  },

  /**
   * 设置热力图透明度
   * @param layerId 图层ID
   * @param opacity 透明度值 (0-1)
   */
  setHeatmapOpacity(layerId: string, opacity: number): void {
    if (!this.heatmapLayers[layerId]) {
      console.error(`热力图图层不存在: ${layerId}`)
      return
    }

    try {
      const heatmapLayer = this.heatmapLayers[layerId]
      heatmapLayer.layer.setOpacity(opacity)
      heatmapLayer.options.opacity = opacity
      console.log(`热力图透明度设置成功，图层: ${layerId}，透明度: ${opacity}`)
    } catch (error) {
      console.error('设置热力图透明度失败:', error)
    }
  },

  /**
   * 设置热力图半径
   * @param layerId 图层ID
   * @param radius 半径值
   */
  setHeatmapRadius(layerId: string, radius: number): void {
    if (!this.heatmapLayers[layerId]) {
      console.error(`热力图图层不存在: ${layerId}`)
      return
    }

    try {
      const heatmapLayer = this.heatmapLayers[layerId]
      heatmapLayer.layer.radius = radius
      heatmapLayer.options.radius = radius
      console.log(`热力图半径设置成功，图层: ${layerId}，半径: ${radius}`)
    } catch (error) {
      console.error('设置热力图半径失败:', error)
    }
  },

  /**
   * 移除热力图图层
   * @param layerId 图层ID
   */
  removeHeatmapLayer(layerId: string): void {
    if (!this.heatmapLayers[layerId]) {
      console.error(`热力图图层不存在: ${layerId}`)
      return
    }

    try {
      const heatmapLayer = this.heatmapLayers[layerId]
      
      // 从地图中移除图层
      if (this.map.hasLayer(heatmapLayer.layer)) {
        this.map.removeLayer(heatmapLayer.layer)
      }
      
      // 清理图层对象
      delete this.heatmapLayers[layerId]
      
      console.log(`热力图图层移除成功: ${layerId}`)
    } catch (error) {
      console.error('移除热力图图层失败:', error)
    }
  },

  /**
   * 移除所有热力图图层
   */
  removeAllHeatmapLayers(): void {
    Object.keys(this.heatmapLayers).forEach(layerId => {
      this.removeHeatmapLayer(layerId)
    })
    console.log('所有热力图图层已移除')
  },

  /**
   * 获取热力图图层
   * @param layerId 图层ID
   * @returns 热力图图层对象
   */
  getHeatmapLayer(layerId: string): HeatmapLayer | null {
    return this.heatmapLayers[layerId] || null
  },

  /**
   * 获取所有热力图图层
   * @returns 热力图图层对象数组
   */
  getAllHeatmapLayers(): HeatmapLayer[] {
    return Object.values(this.heatmapLayers)
  },

  // ----------------  实体管理  ----------------
  /**
   * 移除点标记
   * @param pointId 点ID
   */
  removePoint(pointId: string): boolean {
    if (this.entities.points[pointId]) {
      this.map.removeLayer(this.entities.points[pointId])
      delete this.entities.points[pointId]
      console.log(`成功移除点: ${pointId}`)
      return true
    } else {
      console.log(`点不存在，无法移除: ${pointId}`)
      return false
    }
  },

  /**
   * 移除线
   * @param lineId 线ID
   */
  removeLine(lineId: string): void {
    if (this.entities.lines[lineId]) {
      this.map.removeLayer(this.entities.lines[lineId])
      delete this.entities.lines[lineId]
    }
  },

  /**
   * 移除面
   * @param polygonId 面ID
   */
  removePolygon(polygonId: string): void {
    if (this.entities.polygons[polygonId]) {
      this.map.removeLayer(this.entities.polygons[polygonId])
      delete this.entities.polygons[polygonId]
      
      // 同时移除对应的标签（如果存在）
      const labelId = `label_${polygonId}`
      if (this.entities.polygons[labelId]) {
        this.map.removeLayer(this.entities.polygons[labelId])
        delete this.entities.polygons[labelId]
      }
    }
  },

  /**
   * 移除所有点标记
   */
  removeAllPoints(): void {
    Object.keys(this.entities.points).forEach(id => this.removePoint(id))
  },

  /**
   * 移除所有线
   */
  removeAllLines(): void {
    Object.keys(this.entities.lines).forEach(id => this.removeLine(id))
  },

  /**
   * 移除所有面
   */
  removeAllPolygons(): void {
    Object.keys(this.entities.polygons).forEach(id => this.removePolygon(id))
  },

  /**
   * 移除所有矢量图层
   */
  removeAllVectorLayers(): void {
    this.removeAllPoints()
    this.removeAllLines()
    this.removeAllPolygons()
    this.removeAllHeatmapLayers()
  },

  // ----------------  地图控制  ----------------
  /**
   * 旋转地图
   * @param angle 旋转角度
   */
  rotateMap(angle: number): void {
    if (!this.map) return

    try {
      this.map.setBearing(angle)
    } catch (error) {
      console.error('地图旋转失败:', error)
    }
  },

  /**
   * 获取当前旋转角度
   */
  getCurrentRotation(): number {
    if (!this.map) return 0
    return this.map.getBearing ? this.map.getBearing() : 0
  },

  /**
   * 设置地图中心点
   * @param center 中心点坐标
   * @param animate 是否动画
   */
  setCenter(center: [number, number], animate: boolean = true): void {
    if (!this.map) return

    if (animate) {
      this.map.panTo(center, {
        animate: true,
        duration: 0.5
      })
    } else {
      this.map.setView(center, this.map.getZoom())
    }
  },

  /**
   * 设置缩放级别
   * @param zoom 缩放级别
   * @param animate 是否动画
   */
  setZoom(zoom: number, animate: boolean = true): void {
    if (!this.map) return

    if (animate) {
      this.map.setZoom(zoom, { animate: true })
    } else {
      this.map.setZoom(zoom)
    }
  },

  /**
   * 获取地图中心点
   */
  getCenter(): [number, number] {
    if (!this.map) return [0, 0]
    const center = this.map.getCenter()
    return [center.lat, center.lng]
  },

  /**
   * 获取当前缩放级别
   */
  getZoom(): number {
    if (!this.map) return 0
    return this.map.getZoom()
  },

  /**
   * 获取地图实例
   */
  getMap(): any {
    return this.map
  },

  /**
   * 检查地图是否准备就绪
   */
  isReady(): boolean {
    return this.isMapReady
  },

  /**
   * 获取地图加载状态
   */
  getMapLoadingState(): boolean {
    return this.isMapLoading
  },

  /**
   * 设置地图背景颜色
   * @param color 背景颜色值
   */
  setMapBackgroundColor(color: string): void {
    if (this.map) {
      this.map.getContainer().style.backgroundColor = color
    }
  },

  // ----------------  回调设置  ----------------
  /**
   * 设置地面点击回调
   * @param callback 回调函数
   */
  setGroundClickCallback(callback: (position: { longitude: number, latitude: number }, event: any) => void) {
    this.groundClickCallback = callback
  },

  /**
   * 设置地图加载完成回调
   * @param callback 回调函数
   */
  setMapReadyCallback(callback: (result: { map: any, layers: any }) => void) {
    this.mapReadyCallback = callback
  },

  /**
   * 显示缩放提示消息
   * @param message 提示消息
   */
  showMessage(message: string): void {
    // console.log(`缩放提示: ${message}`)
    // 使用简单的浏览器提示
    if (typeof window !== 'undefined') {
      // 创建简单的提示元素
      const notification = document.createElement('div')
      notification.style.cssText = `
        position: fixed;
        top: 50%;
        right: 50%;
        transform: translate(50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 12px 20px;
        border-radius: 6px;
        font-size: 14px;
        z-index: 10000;
        pointer-events: none;
        transition: opacity 0.3s ease;
      `
      notification.textContent = message
      document.body.appendChild(notification)
      
      // 3秒后自动移除
      setTimeout(() => {
        notification.style.opacity = '0'
        setTimeout(() => {
          if (document.body.contains(notification)) {
            document.body.removeChild(notification)
          }
        }, 300)
      }, 3000)
    }
  },

  // ----------------  实体查找  ----------------
  /**
   * 根据实体ID查找实体
   * @param entityId 实体ID
   * @returns 实体对象或null
   */
  findEntityById(entityId: string): any {
    return this.entities.points[entityId] || 
           this.entities.lines[entityId] || 
           this.entities.polygons[entityId] || null
  },

  /**
   * 根据条件查找实体
   * @param predicate 查找条件函数
   * @returns 实体数组
   */
  findEntitiesByCondition(predicate: (entity: any) => boolean): any[] {
    const entities: any[] = []
    
    Object.values(this.entities.points).forEach(entity => {
      if (predicate(entity)) entities.push(entity)
    })
    Object.values(this.entities.lines).forEach(entity => {
      if (predicate(entity)) entities.push(entity)
    })
    Object.values(this.entities.polygons).forEach(entity => {
      if (predicate(entity)) entities.push(entity)
    })

    return entities
  },

  // ----------------  销毁清理  ----------------
  /**
   * 销毁地图
   */
  destroy(): void {
    try {
      // 移除所有矢量图层
      this.removeAllVectorLayers()
      
      // 清理地图实例
      if (this.map) {
        this.map.off()
        this.map.remove()
        this.map = null
      }

      // 重置状态
      this.isMapReady = false
      this.isMapLoading = false
      this.currentFloor = 'F1'
      this.entities = {
        points: {},
        lines: {},
        polygons: {},
      }
      this.heatmapLayers = {}
      this.layers = {}
      this.callbacks = {}
      this.groundClickCallback = null
      this.mapReadyCallback = null
      this._entityClickCallbacks.clear()
    } catch (error) {
      console.error('销毁地图时出错:', error)
    }
  },
}

// 创建单例实例
export const mapUtil = MAP_UTIL

// 导出工具函数
export const createMapUtil = () => {
  const util = { ...MAP_UTIL }
  util.entities = { points: {}, lines: {}, polygons: {} }
  util.layers = {}
  return util
}
