# Vue 3 + TypeScript + Vite

# supermap 在 vue3 项目中的使用

可参考官方文档：

- https://iclient.supermap.io/web/introduction/leafletDevelop.html#GetLibs

# 一、安装项目所需依赖

根据官方文档

```shell
npm install @supermapgis/iclient-leaflet
```

# 二、引入依赖

在`main.ts`中引入地图`css`

```css
// 引入iclient-leaflet样式
import 'leaflet/dist/leaflet.css'
```

# 三、在组件中使用

```html
<script setup>
  import { onMounted } from "vue";
  import L from "leaflet";
  import { TiledMapLayer } from "@supermapgis/iclient-leaflet";

  const urlList = ref([
    "https://iserver.supermap.io/iserver/services/map-china400/rest/maps/China",
    // ...
  ]);

  const map = ref();

  function initMap() {
    console.log("初始化");
    map.value = L.map("map", {
      center: [35.25, 102.55],
      zoom: 4,
      oom: 16,
      maxZoom: 18,
      minZoom: 15,
      rotate: true,
      backgroundColor: "rgba(24, 91, 184, 0.2)",
      attributionControl: false,
      logoControl: false,
      zoomControl: false,
      crs: L.CRS.EPSG4326,
      dragging: true,
      touchZoom: true,
      scrollWheelZoom: true,
      doubleClickZoom: true,
      boxZoom: true,
      keyboard: true,
      rotate: true,
      reuseTiles: true,
      updateWhenIdle: true,
    });
    const url = urlList.value[0];
    console.log("url", url);
    new TiledMapLayer(url).addTo(map.value);
  }

  onMounted(() => {
    initMap();
  });
</script>

<template>
  <div id="map" class="home-map-div"></div>
</template>

<style scoped>
  .home-map-div {
    margin: 0 auto;
    width: 100%;
    height: 100%;
  }
</style>
```

# 引入地图组件

`App.vue`中引入使用

```html
<div class="home-map">
  <HelloWorld style="width: 100%;height: 100%;" />
</div>
```
