<script setup lang="ts">
import HelloWorld from './components/HelloWorld.vue'
import HomeMap from './components/homeMap.vue';
import TestMap from './components/TestMap.vue';
import { ref } from 'vue'

const currentView = ref('test') // 'home', 'test', 'hello'
</script>

<template>
  <div class="home">
    <div class="home-header">
      <div class="home-logo">
        <a href="https://vite.dev" target="_blank">
          <img src="/vite.svg" class="logo" alt="Vite logo" />
        </a>
        <a href="https://vuejs.org/" target="_blank">
          <img src="./assets/vue.svg" class="logo vue" alt="Vue logo" />
        </a>
      </div>

      <div class="view-switcher">
        <button @click="currentView = 'test'" :class="{ active: currentView === 'test' }">测试地图</button>
        <button @click="currentView = 'home'" :class="{ active: currentView === 'home' }">主地图</button>
        <button @click="currentView = 'hello'" :class="{ active: currentView === 'hello' }">Hello World</button>
      </div>
    </div>

    <div class="home-map">
      <TestMap v-if="currentView === 'test'" style="width: 100%;height: 100%;"/>
      <HomeMap v-else-if="currentView === 'home'" style="width: 100%;height: 100%;"/>
      <HelloWorld v-else-if="currentView === 'hello'" style="width: 100%;height: 100%;"/>
    </div>
  </div>
</template>

<style scoped>
.home {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  margin: 0;
  padding: 0;
}

.home-header {
  width: 100%;
  height: 6em;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  background: #f5f5f5;
  border-bottom: 1px solid #ddd;
}

.home-logo {
  display: flex;
  align-items: center;
  gap: 10px;
}

.view-switcher {
  display: flex;
  gap: 10px;
}

.view-switcher button {
  padding: 8px 16px;
  border: 1px solid #ddd;
  background: white;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s;
}

.view-switcher button:hover {
  background: #f0f0f0;
}

.view-switcher button.active {
  background: #409eff;
  color: white;
  border-color: #409eff;
}

.home-map {
  flex: 1;
  overflow: hidden;
}

.logo {
  height: 4em;
  will-change: filter;
  transition: filter 300ms;
}

.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}

.logo.vue:hover {
  filter: drop-shadow(0 0 2em #42b883aa);
}
</style>
