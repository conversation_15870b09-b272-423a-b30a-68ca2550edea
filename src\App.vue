<script setup lang="ts">
import HelloWorld from './components/HelloWorld.vue'
import HomeMap from './components/homeMap.vue';
</script>

<template>
  <div class="home">
    <div class="home-logo">
      <a href="https://vite.dev" target="_blank">
        <img src="/vite.svg" class="logo" alt="Vite logo" />
      </a>
      <a href="https://vuejs.org/" target="_blank">
        <img src="./assets/vue.svg" class="logo vue" alt="Vue logo" />
      </a>
    </div>

    <div class="home-map">
      <!-- <HelloWorld style="width: 100%;height: 100%;"/> -->
       <HomeMap style="width: 100%;height: 100%;"/>
    </div>
  </div>


</template>

<style scoped>
.home {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  margin: 0;
  padding: 0;
}

.home-logo {
  width: 100%;
  height: 6em;
  display: flex;
  justify-content: center;
  align-items: center;
}

.home-map {
  /* background-color: red; */
  flex: 1;
}

.logo {
  height: 6em;
  will-change: filter;
  transition: filter 300ms;
}

.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}

.logo.vue:hover {
  filter: drop-shadow(0 0 2em #42b883aa);
}

</style>
